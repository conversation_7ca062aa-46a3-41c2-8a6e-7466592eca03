<script lang="ts">
    import PageLoader from "$lib/common/components/PageLoader.svelte";
    import NoPermissionView from "$lib/common/components/NoPermissionView.svelte";
    import type { PaginatedDataWrapper } from "$lib/common/models/base_model";
    import { showErrorToast } from "$lib/common/utils/common-utils";
    import { userPermissions } from "$lib/common/utils/store";
    import { PresenterProvider } from "$lib/PresenterProvider";
    import FinalGoodsTable from "$lib/final_goods/components/FinalGoodsTable.svelte";
    import type {
        IFinalGoodOverview,
        IFinalGoodVariationOverview,
    } from "$lib/final_goods/models/IFinalGoods";
    import { AppPermissions } from "$lib/users/sub-features/permissions/models/AppPermissions";
    import { onMount } from "svelte";
    import { goto } from "$app/navigation";
    let selectedRowsMap: Map<number, IFinalGoodOverview> = new Map();

    let isLoading = true;

    let paginationData: PaginatedDataWrapper<IFinalGoodOverview> = {
        pageSize: 10,
        pagination: {
            currentPage: 1,
            data: [],
            totalData: 0,
            totalPages: 0,
        },
        onPageChange: () => {},
    };

    const loadData = async (page: number) => {
        if (!$userPermissions.includes(AppPermissions.RAW_MATERIAL.READ)) {
            isLoading = false;
            return;
        }

        isLoading = true;
        const response = await PresenterProvider.finalGoodsPresenter.getFinalGoodss(
            page,
            paginationData.pageSize,
            paginationData.searchText
        );
        if (!response.success) {
            showErrorToast(response.message);
            return goto("final-goods/add");
        }
        paginationData.pagination = response.data;
        paginationData.onPageChange = loadData;
        paginationData = paginationData;
        isLoading = false;
    };
    onMount(async () => {
        loadData(1);
    });
</script>

<svelte:head><title>Final Goods</title></svelte:head>

{#if isLoading}
    <PageLoader />
{:else if !$userPermissions.includes(AppPermissions.RAW_MATERIAL.READ)}
    <NoPermissionView />
{:else}
    <FinalGoodsTable
        {paginationData}
        bind:selectedRowsMap
        onSearchClear={() => {
            loadData(1);
        }}
    />
{/if}
