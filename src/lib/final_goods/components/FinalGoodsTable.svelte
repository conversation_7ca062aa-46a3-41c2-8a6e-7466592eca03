<script lang="ts">
    import { Spinner } from "flowbite-svelte";
    import {
        capitalizeFirstWord,
        debounce,
        getPastDate,
        showErrorToast,
    } from "$lib/common/utils/common-utils";
    import { onMount } from "svelte";
    import { goto } from "$app/navigation";
    import Loader from "$lib/common/components/Loader.svelte";
    import type { IFinalGoodOverview } from "../models/IFinalGoods";
    import type { PaginatedDataWrapper } from "$lib/common/models/base_model";
    import PaginationButtons from "$lib/common/components/PaginationButtons.svelte";
    import { PresenterProvider } from "$lib/PresenterProvider";
    import { downloadAllCsv, downloadCsv } from "$lib/final_goods/utils/FinalGoodsUtils";

    const exportAllDate: {
        startDate: Date;
        endDate: Date;
        loading: boolean;
        exportAllLoading: boolean;
    } = {
        startDate: getPastDate(new Date(), 365),
        endDate: new Date(),
        loading: false,
        exportAllLoading: false,
    };
    const headers = [
    // { key: "id", label: "SR No." },
    { key: "name", label: "Name", format: capitalizeFirstWord },
    { key: "unit", label: "Unit",format: (u:{name:string}) => capitalizeFirstWord(u?.name) || "" },
    {key:"description", label:"Description"},
    // {key:"expire_days", label:"Expire Days"},
    {key:"status", label:"Status", format:capitalizeFirstWord},
    
];

    export let searchTerm: string = "";
    export let paginationData: PaginatedDataWrapper<IFinalGoodOverview>;
    export let onSearchClear: () => void = () => {};

    let isLoading: boolean = false;
    let isSearching: boolean = false;
    let filteredData: IFinalGoodOverview[] = [];
    let alreadyFetchedData: IFinalGoodOverview[] = [];

    $:(()=>{
        console.log(filteredData)
    })
    let debounceSearch = debounce(async (e: any) => {
        if (searchTerm.trim().length === 0) {
            paginationData.searchText = undefined;
            onSearchClear();
            return;
        }
        if (e.target.value.trim().length > 2) {
            searchTerm = e.target.value.trim();
            paginationData.searchText = searchTerm;
            // filteredData = paginationData.pagination.data.filter((data) =>
            //     data.name.toLowerCase().includes(searchTerm.toLowerCase())
            // );
            filteredData =[];
            if (filteredData.length === 0) {
                isSearching = true;
                const result = await PresenterProvider.finalGoodsPresenter.getFinalGoodss(
                    1,
                    paginationData.pageSize,
                    e.target.value.trim()
                );
                if (!result.success) {
                    return showErrorToast(result.message);
                } else {
                    filteredData = result.data.data;
                    isSearching = false;
                }
            }
        } else {
            filteredData = alreadyFetchedData;
        }
    }, 300);

    onMount(async () => {
        filteredData = paginationData.pagination.data;
        alreadyFetchedData = paginationData.pagination.data;
    });

    // Selection state
    export let selectedRowsMap: Map<number, IFinalGoodOverview> = new Map();
    // let selectedRows :IFinalGoodsOverview[]=[];
    let selectAll = false;

    // Toggle row selection
    function toggleRowSelection(row: IFinalGoodOverview, id: number) {
        if (selectedRowsMap.has(id)) {
            selectedRowsMap.delete(id);
        } else {
            selectedRowsMap.set(id, row);
        }
    }

    // Toggle all selections
    function toggleSelectAll() {
        if (selectAll) {
            filteredData.forEach((rmData) => {
                selectedRowsMap.set(rmData.id, rmData);
            });
        } else {
            selectedRowsMap.clear();
            selectAll = false;
        }
        selectedRowsMap = selectedRowsMap;
    }
</script>

{#if isLoading}
    <div class="flex h-[80vh] w-[100%] items-center justify-center">
        <Spinner size={"10"} color="gray" />
    </div>
{:else}
    <h1
        class="mb-4 font-primary text-2xl font-medium capitalize tracking-[0px] text-black dark:text-gray-100"
    >
        Final Goods
    </h1>

    <div class="flex my-3 px-1 flex-col md:flex-row items-center gap-4 justify-end">
        <!-- Export Button -->
        <div class="h-full flex items-center">
            <button
                id="dropdownActionButton"
                data-dropdown-toggle="dropdownAction"
                class="inline-flex items-center justify-center rounded-lg border border-gray-300 text-gray-600 px-4 py-2 text-sm font-medium hover:bg-gray-100 focus:outline-none focus:ring-4 focus:ring-red-300"
                type="button"
                disabled={exportAllDate.exportAllLoading}
                on:click={async () => {
                    exportAllDate.exportAllLoading = true;
                    await downloadAllCsv(exportAllDate.startDate, exportAllDate.endDate);
                    exportAllDate.exportAllLoading = false;
                }}
            >
                {#if exportAllDate.exportAllLoading}
                    <div class="flex gap-2 items-center justify-center">
                        Loading <Spinner class="size-5" />
                    </div>
                {:else}
                    Export All
                {/if}
            </button>
        </div>
    </div>

    <div class="relative overflow-x-auto font-primary shadow-md sm:rounded-lg">
        <div
            class="flex-column flex flex-wrap items-center justify-between space-y-4 rounded-lg bg-[#E3E3E3] p-4 text-sm focus:ring-primary-500 dark:bg-[#131313] md:flex-row md:space-y-0"
        >
            <label for="table-search" class="sr-only">Search</label>

            <div class="relative">
                <div
                    class="rtl:inset-r-0 pointer-events-none absolute inset-y-0 start-0 flex items-center ps-3"
                >
                    <img src="/images/svg/search.svg" alt="l" width="15px" />
                </div>
                <input
                    type="text"
                    id="franchise-search"
                    bind:value={searchTerm}
                    on:input={debounceSearch}
                    class="block w-96 rounded-lg p-2 ps-10 text-sm text-gray-900 focus:border-primary-500 focus:ring-primary-500 dark:bg-primary-800 dark:text-white"
                    placeholder="Search by name"
                />
                {#if isSearching}
                    <div class="absolute right-0 top-0 bottom-0 flex items-center justify-center">
                        <Loader />
                    </div>
                {/if}
            </div>
            <div class="space-x-4">
                <button
                    id="dropdownActionButton"
                    data-dropdown-toggle="dropdownAction"
                    class="inline-flex items-center rounded-lg border border-gray-300 bg-white px-3 py-1.5 text-sm font-medium text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-4 focus:ring-gray-100 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:border-gray-600 dark:hover:bg-gray-700 dark:focus:ring-gray-700"
                    type="button"
                    on:click={() => {
                        goto("/admin/final-goods/add");
                    }}
                >
                    Add New
                </button>
                <button
                    id="dropdownActionButton"
                    data-dropdown-toggle="dropdownAction"
                    class="inline-flex items-center rounded-lg border border-gray-300 bg-white px-3 py-1.5 text-sm font-medium text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-4 focus:ring-gray-100 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:border-gray-600 dark:hover:bg-gray-700 dark:focus:ring-gray-700"
                    type="button"
                    disabled={exportAllDate.loading}
                    on:click={async () => {
                        exportAllDate.loading = true;
                        await downloadCsv([...selectedRowsMap.values()], "Final Goods");
                        exportAllDate.loading = false;
                    }}
                >
                    {#if exportAllDate.loading}
                        <div class="flex gap-2 items-center justify-center">
                            Loading <Spinner class="size-5" />
                        </div>
                    {:else}
                        Export
                    {/if}
                </button>
            </div>
        </div>

        {#if isSearching}
            <div class="flex h-[30rem] w-full items-center justify-center">
                <Loader />
            </div>
        {:else}
            <div class="max-h-[48vh] overflow-y-auto">
                <table
                    class="w-full text-left text-sm text-gray-500 dark:text-gray-400 rtl:text-right"
                >
                    <thead
                        class="bg-gray-50 font-primary text-base uppercase text-gray-700 dark:bg-gray-700 dark:text-gray-400"
                    >
                        <tr>
                            <th class="px-4 py-3">
                                <input
                                    type="checkbox"
                                    bind:checked={selectAll}
                                    on:change={toggleSelectAll}
                                />
                            </th>
                            <th scope="col" class="px-6 py-3">SR No.</th>
                            {#each headers as heading }
                            <th scope="col" class="px-6 py-3">{heading.label}</th>
                                
                            {/each}
                            <!-- <th scope="col" class="px-6 py-3">SR No.</th>
                            <th scope="col" class="px-6 py-3">Category</th>
                            <th scope="col" class="px-6 py-3">Name</th>
                            <th scope="col" class="px-6 py-3">Unit</th>
                            <th scope="col" class="px-6 py-3">HSN</th>
                            <th scope="col" class="px-6 py-3">GST %</th> -->
                        </tr>
                    </thead>
                    <!-- <tbody>
                        {#each filteredData as row, index}
                            <tr
                                class="border-b bg-white text-gray-600 hover:bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-600"
                            >
                                <td class="px-4 py-4">
                                    <input
                                        type="checkbox"
                                        checked={selectedRowsMap.has(row.id)}
                                        on:change={() => toggleRowSelection(row, row.id)}
                                    />
                                </td>

                                <td class="px-6 py-4">
                                    <a
                                        href={"/admin/final-goods/edit?id=" + row.id}
                                        class="block h-full w-full whitespace-nowrap font-medium"
                                    >
                                        {row.id}
                                    </a>
                                </td>

                                <td class="px-6 py-4">
                                    <a
                                        href={"/admin/final-goods/edit?id=" + row.id}
                                        class="block h-full w-full whitespace-nowrap font-medium"
                                    >
                                        {capitalizeFirstWord(row.categoryName)}
                                    </a>
                                </td>
                                <td class="px-6 py-4">
                                    <a
                                        href={"/admin/final-goods/edit?id=" + row.id}
                                        class="block h-full w-full whitespace-nowrap font-medium"
                                    >
                                        {capitalizeFirstWord(row.name)}
                                    </a>
                                </td>
                                <td class="px-6 py-4">
                                    <a
                                        href={"/admin/final-goods/edit?id=" + row.id}
                                        class="block h-full w-full whitespace-nowrap font-medium"
                                    >
                                        {row.unitName.toUpperCase()}
                                    </a>
                                </td>
                                <td class="px-6 py-4">
                                    <a
                                        href={"/admin/final-goods/edit?id=" + row.id}
                                        class="block h-full w-full whitespace-nowrap font-medium"
                                    >
                                        {row.hsn}
                                    </a>
                                </td>
                                <td class="px-6 py-4">
                                    <a
                                        href={"/admin/final-goods/edit?id=" + row.id}
                                        class="block h-full w-full whitespace-nowrap font-medium"
                                    >
                                        {row.gstPercentage}
                                    </a>
                                </td>
                            </tr>
                        {/each}
                        {#if filteredData.length === 0}
                            <tr class="font-medium text-black dark:text-gray-400">
                                <td colspan="6" class="h-[50vh] text-center">
                                    Yet no final goods
                                </td>
                            </tr>
                        {/if}
                    </tbody> -->
                    <tbody>
                        {#each filteredData as row, i}
                          <tr class="...">
                            <td class="px-4 py-4">
                              <input
                                type="checkbox"
                                checked={selectedRowsMap.has(row.id)}
                                on:change={() => toggleRowSelection(row, row.id)}
                              />
                            </td>


                            <!-- Serial Number Column -->
                            <td class="px-6 py-4">
                                <a
                                  href={"/admin/final-goods/edit?id=" + row.id}
                                  class="block h-full w-full whitespace-nowrap font-medium"
                                >
                                    {i+1}
                                </a>
                              </td>
                        
                            {#each headers as header}
                              <td class="px-6 py-4">
                                <a
                                  href={"/admin/final-goods/edit?id=" + row.id}
                                  class="block h-full w-full whitespace-nowrap font-medium"
                                >
                                  {header.format ? header.format(row[header.key]) : row[header.key]}
                                   <!-- {i+1} -->
                                </a>
                              </td>
                            {/each}
                          </tr>
                        {/each}
                
                        {#if filteredData.length === 0}
                          <tr class="font-medium text-black dark:text-gray-400">
                            <td colspan={headers.length + 1} class="h-[50vh] text-center">
                              Yet no final goods
                            </td>
                          </tr>
                        {/if}
                    </tbody>

                </table>
            </div>
        {/if}
    </div>
    <PaginationButtons {paginationData} />
{/if}
