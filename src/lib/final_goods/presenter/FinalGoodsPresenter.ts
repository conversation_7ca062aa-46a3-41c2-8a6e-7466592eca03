import type { DTO } from "$lib/common/models/BaseDTO";
import { RepoProvider } from "$lib/RepoProvider";
import type {  ValidationErrors } from "$lib/common/utils/types";
import type { IFinalGoodsPresenter, } from "./IFinalGoodsPresenter";
import type {
    IFinalGoodVariationOverview,
    IFinalGoodPutPayload,
    IFinalGoodsPostPayload,
    IFinalGoodOverview,
    IFinalGoodDetailed,
    IFinalGoodVariationNested,
    IFinalGoodVariationPostPayload,
    IFinalGoodVariationDeletePayload,
    IFinalGoodVariationPutPayload
} from "../models/IFinalGoods";
import { FinalGoodsUtils } from "../utils/FinalGoodsUtils";
import type { IItemUnit } from "$lib/item_unit/models/IItemUnit";
import type { IItemCategory } from "$lib/item_category/models/IItemCategory";
import type { PaginatedBaseResponse } from "$lib/common/models/base_model";

export class FinalGoodsPresenter implements IFinalGoodsPresenter {

   
    getFinalGoodss(page: number, pageSize: number, text?:string): Promise<DTO<PaginatedBaseResponse<IFinalGoodOverview>>> {
        return RepoProvider.finalGoodsRepo.getFinalGoodss(page, pageSize,text);
    }

    getVariations(page: number, pageSize: number, text?: string, supplierId?: number): Promise<DTO<PaginatedBaseResponse<IFinalGoodVariationOverview>>> {
        return RepoProvider.finalGoodsRepo.getVariations(page, pageSize, text, supplierId);
    }
    getFinalGoodsById(id: number): Promise<DTO<IFinalGoodDetailed>> {
        return RepoProvider.finalGoodsRepo.getFinalGoodsById(id);
    }
    onSubmit(payload: IFinalGoodsPostPayload): Promise<DTO<null>> {
        return RepoProvider.finalGoodsRepo.create(payload);
    }
    onUpdateFinalGoods(payload: IFinalGoodPutPayload): Promise<DTO<null>> {
        return RepoProvider.finalGoodsRepo.updateFinalGoods(payload);
    }
    onDeleteFinalGoods(ids: number[]): Promise<DTO<null>> {
        return RepoProvider.finalGoodsRepo.deleteFinalGoods(ids);
    }
    onDeleteFinalGoodsById(id: number): Promise<DTO<null>> {
        return RepoProvider.finalGoodsRepo.deleteFinalGoodsById(id);
    }
    onValidate(payload: IFinalGoodVariationOverview): ValidationErrors {
        const errors: ValidationErrors = new Map();
        /* validate */
        console.log(payload);

        const result = FinalGoodsUtils.creationSchema.safeParse(payload);
        console.log(result);

        if (!result.success) {
            result.error.issues.forEach((issue) => {
                errors.set(issue.path[0].toString(), issue.message);
            });
        }

        return errors;
    }


    getItemUnits(page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IItemUnit>>> {
        return RepoProvider.itemUnitRepo.getAll(page, pageSize);
    }
    getItemCategories(page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IItemCategory>>> {
        return RepoProvider.itemCategoryRepo.getAll(page, pageSize);
    }
    addVariation(payload: IFinalGoodVariationPostPayload): Promise<DTO<IFinalGoodVariationNested[] | null>> {
        return RepoProvider.finalGoodsRepo.addVariation(payload);
    }
    updateVariation(payload: IFinalGoodVariationPutPayload): Promise<DTO<IFinalGoodVariationNested | null>> {
        return RepoProvider.finalGoodsRepo.updateVariation(payload);
    }
    deleteVariation(payload: IFinalGoodVariationDeletePayload): Promise<DTO<null>> {
        return RepoProvider.finalGoodsRepo.deleteVariation(payload);
    }
    
}