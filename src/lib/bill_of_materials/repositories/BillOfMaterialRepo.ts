import { BILL_OF_MATERIALS_API_PATH, FINAL_GOODS_VARIATION_API_PATH, RAW_MATERIAL_VARIATION_API_PATH } from "$lib/common/configs/serverConfig";
import {
    getHandledErrorDTO,
    getSuccessDTO,
    getUnhandledErrorDTO,
    type DTO,
} from "$lib/common/models/BaseDTO";
import { fetchData } from "$lib/fetch/utils/fetch-utils";
import type { FetchResult } from "$lib/fetch/models/Fetch";
import { handleError } from "$lib/common/utils/logging";
import type { IBillOfMaterialRepo } from "./IBillOfMaterialRepo";
import type {
    IBillOfMaterial,
    IBillOfMaterialOverview,
    IBillOfMaterialAddRequest,
    IBillOfMaterialUpdateRequest,
} from "../models/IBillOfMaterial";
import type { PaginatedBaseResponse } from "$lib/common/models/base_model";
import type { IFinalGoodVariationOverview } from "$lib/final_goods/models/IFinalGoods";
import type { IRawMaterialVariation } from "$lib/raw_material/models/IRawMaterial";

export class BillOfMaterialRepo implements IBillOfMaterialRepo {

    async getBillOfMaterials(page: number, pageSize: number, text?: string): Promise<DTO<PaginatedBaseResponse<IBillOfMaterialOverview>>> {
        try {
            const params = new URLSearchParams({
                page: page.toString(),
                pageSize: pageSize.toString(),
            });

            if (text && text.trim()) {
                params.append('search', text.trim());
            }

            const result: FetchResult<PaginatedBaseResponse<IBillOfMaterialOverview>> = await fetchData(
                `${BILL_OF_MATERIALS_API_PATH}?${params.toString()}`,
                {
                    method: "GET",
                    headers: {
                        "Content-Type": "application/json",
                    },
                }
            );

            if (result.success) {
                return getSuccessDTO(result.data!);
            } else {
                return getHandledErrorDTO(result.message || "Failed to fetch bill of materials");
            }
        } catch (error) {
            handleError(error);
            return getUnhandledErrorDTO("Failed to fetch bill of materials");
        }
    }

    async getBillOfMaterialById(id: number): Promise<DTO<IBillOfMaterial>> {
        try {
            const result: FetchResult<IBillOfMaterial> = await fetchData(
                `${BILL_OF_MATERIALS_API_PATH}/${id}`,
                {
                    method: "GET",
                    headers: {
                        "Content-Type": "application/json",
                    },
                }
            );

            if (result.success) {
                return getSuccessDTO(result.data!);
            } else {
                return getHandledErrorDTO(result.message || "Failed to fetch bill of material");
            }
        } catch (error) {
            handleError(error);
            return getUnhandledErrorDTO("Failed to fetch bill of material");
        }
    }

    async create(payload: IBillOfMaterialAddRequest): Promise<DTO<null>> {
        try {
            const result: FetchResult<null> = await fetchData(
                BILL_OF_MATERIALS_API_PATH,
                {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                    },
                    body: payload,
                }
            );

            if (result.success) {
                return getSuccessDTO(null);
            } else {
                return getHandledErrorDTO(result.message || "Failed to create bill of material");
            }
        } catch (error) {
            handleError(error);
            return getUnhandledErrorDTO("Failed to create bill of material");
        }
    }

    async updateBillOfMaterial(payload: IBillOfMaterialUpdateRequest): Promise<DTO<null>> {
        try {
            const { id, ...updateData } = payload;
            const result: FetchResult<null> = await fetchData(
                `${BILL_OF_MATERIALS_API_PATH}/${id}`,
                {
                    method: "PUT",
                    headers: {
                        "Content-Type": "application/json",
                    },
                    body: updateData,
                }
            );

            if (result.success) {
                return getSuccessDTO(null);
            } else {
                return getHandledErrorDTO(result.message || "Failed to update bill of material");
            }
        } catch (error) {
            handleError(error);
            return getUnhandledErrorDTO("Failed to update bill of material");
        }
    }

    async deleteBillOfMaterial(ids: number[]): Promise<DTO<null>> {
        try {
            // For bulk delete, we'll need to implement this based on backend API
            // For now, we'll delete one by one
            for (const id of ids) {
                await this.deleteBillOfMaterialById(id);
            }
            return getSuccessDTO(null);
        } catch (error) {
            handleError(error);
            return getUnhandledErrorDTO("Failed to delete bill of material");
        }
    }

    async deleteBillOfMaterialById(id: number): Promise<DTO<null>> {
        try {
            const result: FetchResult<null> = await fetchData(
                `${BILL_OF_MATERIALS_API_PATH}/delete/${id}`,
                {
                    method: "DELETE",
                    headers: {
                        "Content-Type": "application/json",
                    },
                }
            );

            if (result.success) {
                return getSuccessDTO(null);
            } else {
                return getHandledErrorDTO(result.message || "Failed to delete bill of material");
            }
        } catch (error) {
            handleError(error);
            return getUnhandledErrorDTO("Failed to delete bill of material");
        }
    }

    async searchFinalGoodsVariations(page: number, pageSize: number, text?: string): Promise<DTO<PaginatedBaseResponse<IFinalGoodVariationOverview>>> {
        try {
            const params = new URLSearchParams({
                page: page.toString(),
                pageSize: pageSize.toString(),
            });

            if (text && text.trim()) {
                params.append('search', text.trim());
            }

            const result: FetchResult<PaginatedBaseResponse<IFinalGoodVariationOverview>> = await fetchData(
                `${FINAL_GOODS_VARIATION_API_PATH}/variations?${params.toString()}`,
                {
                    method: "GET",
                    headers: {
                        "Content-Type": "application/json",
                    },
                }
            );

            if (result.success) {
                return getSuccessDTO(result.data!);
            } else {
                return getHandledErrorDTO(result.message || "Failed to search final goods variations");
            }
        } catch (error) {
            handleError(error);
            return getUnhandledErrorDTO("Failed to search final goods variations");
        }
    }

    async searchRawMaterialVariations(page: number, pageSize: number, text?: string): Promise<DTO<PaginatedBaseResponse<IRawMaterialVariation>>> {
        try {
            const params = new URLSearchParams({
                page: page.toString(),
                pageSize: pageSize.toString(),
            });

            if (text && text.trim()) {
                params.append('search', text.trim());
            }

            const result: FetchResult<PaginatedBaseResponse<IRawMaterialVariation>> = await fetchData(
                `${RAW_MATERIAL_VARIATION_API_PATH}?${params.toString()}`,
                {
                    method: "GET",
                    headers: {
                        "Content-Type": "application/json",
                    },
                }
            );

            if (result.success) {
                return getSuccessDTO(result.data!);
            } else {
                return getHandledErrorDTO(result.message || "Failed to search raw material variations");
            }
        } catch (error) {
            handleError(error);
            return getUnhandledErrorDTO("Failed to search raw material variations");
        }
    }
}
