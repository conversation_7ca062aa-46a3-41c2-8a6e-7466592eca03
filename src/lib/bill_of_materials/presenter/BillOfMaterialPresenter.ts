import type { DTO } from "$lib/common/models/BaseDTO";
import { RepoProvider } from "$lib/RepoProvider";
import type { ValidationErrors } from "$lib/common/utils/types";
import type { IBillOfMaterialPresenter } from "./IBillOfMaterialPresenter";
import { BillOfMaterialUtils } from "../utils/BillOfMaterialUtils";
import type {
    IBillOfMaterial,
    IBillOfMaterialOverview,
    IBillOfMaterialAddRequest,
    IBillOfMaterialUpdateRequest,
} from "../models/IBillOfMaterial";
import type { PaginatedBaseResponse } from "$lib/common/models/base_model";
import type { IFinalGoodVariationOverview } from "$lib/final_goods/models/IFinalGoods";
import type { IRawMaterialVariation } from "$lib/raw_material/models/IRawMaterial";

export class BillOfMaterialPresenter implements IBillOfMaterialPresenter {

    getBillOfMaterials(page: number, pageSize: number, text?: string): Promise<DTO<PaginatedBaseResponse<IBillOfMaterialOverview>>> {
        return RepoProvider.billOfMaterialRepo.getBillOfMaterials(page, pageSize, text);
    }

    getBillOfMaterialById(id: number): Promise<DTO<IBillOfMaterial>> {
        return RepoProvider.billOfMaterialRepo.getBillOfMaterialById(id);
    }

    onSubmit(payload: IBillOfMaterialAddRequest): Promise<DTO<null>> {
        return RepoProvider.billOfMaterialRepo.create(payload);
    }

    onUpdateBillOfMaterial(payload: IBillOfMaterialUpdateRequest): Promise<DTO<null>> {
        return RepoProvider.billOfMaterialRepo.updateBillOfMaterial(payload);
    }

    onDeleteBillOfMaterial(ids: number[]): Promise<DTO<null>> {
        return RepoProvider.billOfMaterialRepo.deleteBillOfMaterial(ids);
    }

    onDeleteBillOfMaterialById(id: number): Promise<DTO<null>> {
        return RepoProvider.billOfMaterialRepo.deleteBillOfMaterialById(id);
    }

    onValidate(payload: IBillOfMaterialAddRequest): ValidationErrors {
        return BillOfMaterialUtils.validateCreate(payload);
    }

    searchFinalGoodsVariations(page: number, pageSize: number, text?: string): Promise<DTO<PaginatedBaseResponse<IFinalGoodVariationOverview>>> {
        return RepoProvider.billOfMaterialRepo.searchFinalGoodsVariations(page, pageSize, text);
    }

    searchRawMaterialVariations(page: number, pageSize: number, text?: string): Promise<DTO<PaginatedBaseResponse<IRawMaterialVariation>>> {
        return RepoProvider.billOfMaterialRepo.searchRawMaterialVariations(page, pageSize, text);
    }
}
