import type { PaginatedBaseResponse } from "$lib/common/models/base_model";
import type { DTO } from "$lib/common/models/BaseDTO";
import type { ValidationErrors } from "$lib/common/utils/types";
import type { IFinalGoodVariationOverview } from "$lib/final_goods/models/IFinalGoods";
import type { IRawMaterialVariation } from "$lib/raw_material/models/IRawMaterial";
import type {
    IBillOfMaterial,
    IBillOfMaterialOverview,
    IBillOfMaterialAddRequest,
    IBillOfMaterialUpdateRequest,
} from "../models/IBillOfMaterial";

export interface IBillOfMaterialPresenter {
    // CRUD operations
    getBillOfMaterials(page: number, pageSize: number, text?: string): Promise<DTO<PaginatedBaseResponse<IBillOfMaterialOverview>>>;
    getBillOfMaterialById(id: number): Promise<DTO<IBillOfMaterial>>;
    onSubmit(payload: IBillOfMaterialAddRequest): Promise<DTO<null>>;
    onUpdateBillOfMaterial(payload: IBillOfMaterialUpdateRequest): Promise<DTO<null>>;
    onDeleteBillOfMaterial(ids: number[]): Promise<DTO<null>>;
    onDeleteBillOfMaterialById(id: number): Promise<DTO<null>>;

    // Validation
    onValidate(payload: IBillOfMaterialAddRequest): ValidationErrors;

    // Search operations for dropdowns
    searchFinalGoodsVariations(page: number, pageSize: number, text?: string): Promise<DTO<PaginatedBaseResponse<IFinalGoodVariationOverview>>>;
    searchRawMaterialVariations(page: number, pageSize: number, text?: string): Promise<DTO<PaginatedBaseResponse<IRawMaterialVariation>>>;
}
