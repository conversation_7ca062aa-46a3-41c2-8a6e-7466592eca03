<script lang="ts">
    import SearchWithDrop from "$lib/common/components/SearchWithDrop.svelte";
    import { debounce } from "$lib/common/utils/common-utils";
    import type { IFinalGoodVariationOverview } from "$lib/final_goods/models/IFinalGoods";
    import { PresenterProvider } from "$lib/PresenterProvider";

    export let isLabel: boolean = true;
    export let labelText: string = "Final Goods Variation";
    export let selected: IFinalGoodVariationOverview | null = null;
    export let onSelected: (data: IFinalGoodVariationOverview | null) => void;
    export let disabled: boolean = false;
    export let errorMap: Map<string, string> | null = null;
    export let fieldName: string = "finalGoodsVariationId";

    let searchTerm: string = "";
    let fetchedData: IFinalGoodVariationOverview[] = [];
    let doingSearch: boolean = false;

    const getData = async () => {
        if (!searchTerm.trim()) {
            fetchedData = [];
            return;
        }

        doingSearch = true;
        try {
            const res = await PresenterProvider.billOfMaterialPresenter.searchFinalGoodsVariations(1, 20, searchTerm);
            if (res.success && res.data) {
                fetchedData = res.data.data;
            } else {
                fetchedData = [];
            }
        } catch (error) {
            console.error("Error searching final goods variations:", error);
            fetchedData = [];
        }
        doingSearch = false;
    };

    const debounceSearch = debounce(getData, 600);

    const doSearch = (search: string) => {
        searchTerm = search;
        debounceSearch();
    };

    const getSelectedDisplayText = (): string | null => {
        if (!selected) return null;
        return `${selected.name} (${selected.sku})`;
    };

    // Transform data to include display text for SearchWithDrop
    $: transformedData = fetchedData.map(item => ({
        ...item,
        displayText: `${item.name} (${item.sku})`
    }));
</script>

<SearchWithDrop
    {isLabel}
    {disabled}
    {errorMap}
    {fieldName}
    label={labelText}
    bind:searchTerm
    level="displayText"
    loading={doingSearch}
    placeholder="Search final goods variation..."
    searchInput={doSearch}
    selected={getSelectedDisplayText()}
    selectedObj={selected}
    selectedFunc={(data) => {
        fetchedData = [];
        searchTerm = "";
        onSelected(data);
        selected = data;
    }}
    bind:filteredData={transformedData}
/>
