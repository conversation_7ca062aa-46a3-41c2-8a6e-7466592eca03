import { Optional } from "sequelize";
import { InterfaceMetaData, IQueryFilter } from "../../../core/CoreInterfaces";
import { IFinalGoodsVariationZodPayload } from "../validations/finalGoodsValidationsSchema";


interface FinalGoodsVariationAttrbutes extends Omit<IFinalGoodsVariationZodPayload, 'attributeValuesIds'>, InterfaceMetaData {
    id: number;
}

interface ICreateFinalGoodsVariationAttributes extends Optional<FinalGoodsVariationAttrbutes, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt' > { }

interface FinalGoodsVariationFilter extends IQueryFilter {
    name?: string,
    sku?: string
}

interface FinalGoodsVariationPayload extends IFinalGoodsVariationZodPayload { }

interface UpdateFinalGoodVariation extends FinalGoodsVariationPayload {
    id: number;
}


export { FinalGoodsVariationAttrbutes, FinalGoodsVariationPayload, ICreateFinalGoodsVariationAttributes, UpdateFinalGoodVariation, FinalGoodsVariationFilter }