import { FinalGoodsVariationAttrbutes } from "./IFinalGoodsVariation";
import { ParsedItemCategory } from "../../item_category/models/IItemCategory";
import { AuditData, IIdTitle, InterfaceMetaData, IQueryFilter } from "../../../core/CoreInterfaces";
import { IFinalGoodsCreationZodPayload, IFinalGoodsUpdateZodPayload, IFinalGoodsZodPayload } from "../validations/finalGoodsValidationsSchema";
import { IItemUnit, ParsedItemUnit } from "../../item_unit/models/IItemUnit";
import { FinalGoodsAndCategoriesLinkingModel } from "../model/finalGoodsAndCategoriesLinking";
import { ItemCategoryTable } from "../../item_category/database/ItemCategoryTable";
import { StockAttributes } from "./Stock";
import { TaxRateTable } from "../../tax-rate/interface/taxRate";
import { FinalGoodsVariationAndAttrbutesRelationAttributes } from "./FinalGoodsVariationAndAttributesRelation";
import { IItemAttributeValue } from "../../item_attribute_value/models/IItemAttributeValue";
import { IItemAttribute } from "../../item_attribute/models/IItemAttribute";

enum FINAL_GOODS_STATUS {
    ACTIVE = "active",
    INACTIVE = "inactive",
}

interface IFinalGoodsCreationPayload extends IFinalGoodsCreationZodPayload {
    createdById: number;
}

interface IFinalGoodsCreationAttributes extends Omit<IFinalGoodsZodPayload, 'categoryIds'> {
    createdById: number;
}


interface IFinalGoodsAttributes extends IFinalGoodsCreationAttributes, InterfaceMetaData {
    status: FINAL_GOODS_STATUS;
}

interface FinalGoodsFilter extends IQueryFilter {

}


interface IFinalGoodOverview {
    id: number;
    categories: IIdTitle[];
    unit: IIdTitle;
    name: string;
    description: string;
    hsn: string;
    images: string[];
    status: FINAL_GOODS_STATUS;
    createdAt: Date;
    createdBy: IIdTitle;
}

interface IFinalGoodsUpdatePayload extends IFinalGoodsUpdateZodPayload {
    updatedById: number;
}

interface IFinalGoods extends IFinalGoodsAttributes {
    finalGoodsCategories: FinalGoodsAndCategoriesLinkingModel & { category: ItemCategoryTable }[],
    unit: IItemUnit | null,
}


interface IFinalGoodVariationNested {
    id: number;
    tax: IIdTitle;
    name: string;
    sku: string;
    price: number;
    expiryDays: number;
    discount: number;
    moq: number;
    msq: number;
    images: string[];
    attributes: {
        attribute: IItemAttribute;
        attributeValue: IItemAttributeValue;
    }[];
}

interface IFinalGoodDetailed extends IFinalGoodOverview {
    variations: IFinalGoodVariationNested[];
}


export { FINAL_GOODS_STATUS as FinalGoodsStatus, IFinalGoodsAttributes, FinalGoodsFilter, IFinalGoodOverview, IFinalGoodsCreationAttributes, IFinalGoodsCreationPayload, IFinalGoodsUpdatePayload, IFinalGoods, IFinalGoodDetailed,IFinalGoodVariationNested }