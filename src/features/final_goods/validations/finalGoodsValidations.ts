import { NextFunction, Request, Response } from "express";
import { finalGoodsValidationSchema } from "./finalGoodsValidationsSchema";
import { HelperMethods } from "../../../core/HelperMethods";
import { extractAllZodErrors } from "../../../core/utils";
import { CoreSchemas } from "../../../core/CoreSchemas";

class finalGoodsValidations {
    static validateCreate = (req: Request, res: Response, next: NextFunction) => {

        const result = finalGoodsValidationSchema.create.safeParse(req.body);

        if (!result.success) {
            res.status(400).send(HelperMethods.ValidationError(extractAllZodErrors(result.error)));
            return;
        }
        return next();
    }

    static validateGetById = (req: Request, res: Response, next: NextFunction) => {

        const result = finalGoodsValidationSchema.finalGoodsById.safeParse(req.params);

        if (!result.success) {
            let errorMessage = "";
            if (result.error.errors[0].message === "Required") {
                errorMessage = result.error.errors[0].path[0] + " is required";
            }
            else {
                errorMessage = result.error.errors[0].message;
            }

            res.status(400).send(HelperMethods.getErrorResponse(errorMessage));
            return;
        }

        return next();
    }

    static validateDelete = (req: Request, res: Response, next: NextFunction) => {

        const result = finalGoodsValidationSchema.finalGoodsDeleteValidationSchema.safeParse({ params: req.params, body: req.body });

        if (!result.success) {
            let errorMessage = "";
            if (result.error.errors[0].message === "Required") {
                errorMessage = result.error.errors[0].path[0] + " is required";
            }
            else {
                errorMessage = result.error.errors[0].message;
            }

            res.status(400).send(HelperMethods.getErrorResponse(errorMessage));
            return;
        }

        return next();
    }

    static validateList = (req: Request, res: Response, next: NextFunction) => {

        const result = finalGoodsValidationSchema.finalGoodsList.safeParse({ ...req.params, ...req.body, ...req.query });

        if (!result.success) {
            let errorMessage = "";
            if (result.error.errors[0].message === "Required") {
                errorMessage = result.error.errors[0].path[0] + " is required";
            }
            else {
                errorMessage = result.error.errors[0].message;
            }

            res.status(400).send(HelperMethods.getErrorResponse(errorMessage));
            return;
        }

        return next();
    }

    static validateUpdate = (req: Request, res: Response, next: NextFunction) => {

        const result = finalGoodsValidationSchema.finalGoodUpdate.safeParse({ ...req.params, ...req.body, ...req.query });

        if (!result.success) {
            let errorMessage = "";
            if (result.error.errors[0].message === "Required") {
                errorMessage = result.error.errors[0].path[0] + " is required";
            }
            else {
                errorMessage = result.error.errors[0].message;
            }

            res.status(400).send(HelperMethods.getErrorResponse(errorMessage));
            return;
        }

        return next();
    }

    static validateUpdateVariations = (req: Request, res: Response, next: NextFunction) => {

        const result = finalGoodsValidationSchema.updateVariation.safeParse({ ...req.params, ...req.body, ...req.query });

        if (!result.success) {
            let errorMessage = "";
            if (result.error.errors[0].message === "Required") {
                errorMessage = result.error.errors[0].path[0] + " is required";
            }
            else {
                errorMessage = result.error.errors[0].message;
            }

            res.status(400).send(HelperMethods.getErrorResponse(errorMessage));
            return;
        }

        return next();
    }

    static validateDeleteVariation = (req: Request, res: Response, next: NextFunction) => {

        const result = finalGoodsValidationSchema.deleteVariation.safeParse(req.body);

        if (!result.success) {
            res.status(400).send(HelperMethods.ValidationError(extractAllZodErrors(result.error)));
            return;
        }
        return next();
    }

    static addNewVariation = (req: Request, res: Response, next: NextFunction) => {
        const idRes = CoreSchemas.updateByIdSchema.safeParse(req.params)

        if (!idRes.success) {
            res.status(400).send(HelperMethods.ValidationError(extractAllZodErrors(idRes.error)));
            return;
        }
        
        const result = finalGoodsValidationSchema.addNewVariation.safeParse(req.body);

        if (!result.success) {
            res.status(400).send(HelperMethods.ValidationError(extractAllZodErrors(result.error)));
            return;
        }
        return next();
    }
}

export default finalGoodsValidations