import { Transaction } from "sequelize";
import { FinalGoodsFilter, IFinalGoodDetailed, IFinalGoodOverview, IFinalGoodsCreationPayload, IFinalGoodsUpdatePayload, IFinalGoodVariationNested } from "../interface/IFinalGood";
import { APIBaseResponse, PaginatedBaseResponse } from "../../../core/CoreInterfaces";
import { FinalGoodsVariationPayload, UpdateFinalGoodVariation } from "../interface/IFinalGoodsVariation";
import { FinalGoodsTable } from "../model/finalGoodsTable";
import { FinalGoodsVariationTable } from "../model/finalGoodsVariationTable";

export interface IFinalGoodsRepo {
    create(product: IFinalGoodsCreationPayload, createdBy: number, transaction: Transaction): Promise<APIBaseResponse<null>>;

    update(id: number, product: IFinalGoodsUpdatePayload, updatedBy: number, transaction: Transaction): Promise<APIBaseResponse<FinalGoodsTable | null>>;

    updateVariation(id: number, payload: UpdateFinalGoodVariation, updatedBy: number, transaction: Transaction): Promise<APIBaseResponse<IFinalGoodVariationNested | null>>;

    addNewVariationOfFinalGoods(finalGoodsId: number, payload: FinalGoodsVariationPayload[], createdBy: number, transaction: Transaction): Promise<APIBaseResponse<IFinalGoodVariationNested[] | null>>;

    delete(id: number, user_id: number, transaction: Transaction): Promise<APIBaseResponse<FinalGoodsTable | null>>;

    getById(id: number, transaction: Transaction): Promise<APIBaseResponse<IFinalGoodDetailed | null>>;

    getAll(page: number, limit: number, filters: FinalGoodsFilter, transaction: Transaction): Promise<APIBaseResponse<PaginatedBaseResponse<IFinalGoodOverview> | null>>

    getAllVariation(page: number, limit: number, filters: FinalGoodsFilter, transaction: Transaction
    ): Promise<APIBaseResponse<PaginatedBaseResponse<IFinalGoodVariationNested> | null>>

    getVariationById(id: number,transaction: Transaction): Promise<APIBaseResponse<IFinalGoodVariationNested | null>>
    
    deleteVariation(ids: number[], user_id: number, transaction: Transaction): Promise<APIBaseResponse< null>>;
}
