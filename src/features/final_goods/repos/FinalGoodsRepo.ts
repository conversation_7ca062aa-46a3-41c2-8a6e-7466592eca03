import { IFinalGoodsRepo } from "./IFinalGoodsRepo";
import { Op, Transaction } from "sequelize";
import { APIBaseResponse, PaginatedBaseResponse } from "../../../core/CoreInterfaces";
import { PaginationProvider } from "../../pagination/PaginationProvider";
import { HelperMethods } from "../../../core/HelperMethods";
import { FinalGoodsFilter, IFinalGoodDetailed, IFinalGoodOverview, IFinalGoodsCreationPayload, IFinalGoodsUpdatePayload, IFinalGoodVariationNested } from "../interface/IFinalGood";
import { FinalGoodsVariationTable } from "../model/finalGoodsVariationTable";
import { StockTable } from "../model/stockTable";
import { FinalGoodsVariationAndAttributesRelationTable } from "../model/FinalGoodsVariationAndAttributesRelationTable";
import { FinalGoodsVariationFilter, FinalGoodsVariationPayload, UpdateFinalGoodVariation } from "../interface/IFinalGoodsVariation";
import { finalGoodsInclude, finalGoodsVariationsInclude } from "../includes/finalGoodsInclude";
import { FinalGoodsTable } from "../model/finalGoodsTable";
import { FinalGoodsAndCategoriesLinkingModel } from "../model/finalGoodsAndCategoriesLinking";
import { RepoProvider } from "../../../core/RepoProvider";
import { title } from "process";

export class PostgresFinalGoodsRepo implements IFinalGoodsRepo {

    private _categoryRepo = RepoProvider.itemCategoryRepo
    private _attributeValueRepo = RepoProvider.itemAttributeValueRepo
    private _taxRateRepo = RepoProvider.taxRateRepo
    private _itemUnitRepo = RepoProvider.itemUnitRepo

    async create(
        payload: IFinalGoodsCreationPayload,
        createdBy: number,
        transaction: Transaction,
    ): Promise<APIBaseResponse<null>> {
        try {
            const isFinalGoodsExists = await FinalGoodsTable.findOne({
                where: {
                    name: payload.finalGood.name,
                },
                transaction,
            })

            if (isFinalGoodsExists) {
                throw new Error(`Final goods with name ${payload.finalGood.name} already exists.`);
            }
            const unitId = payload.finalGood.unitId
            const itemUnitResponse = await this._itemUnitRepo.getById(unitId, transaction)

            if (!itemUnitResponse.success) {
                throw new Error(`Unit data not exists of this id (${unitId})`)
            }

            const createdFinalGood = await FinalGoodsTable.create({
                name: payload.finalGood.name,
                images: payload.finalGood.images,
                unitId: unitId,
                description: payload.finalGood.description,
                hsn: payload.finalGood.hsn,
                createdById: createdBy,
            }, {
                transaction, userId: (createdBy)
            })

            if (!createdFinalGood) {
                throw new Error("Failed to create final goods.");
            }

            for (const categoryId of payload.finalGood.categoryIds) {

                const createdCategory = await this._categoryRepo.getById(categoryId, transaction)
                if (!createdCategory.success) {
                    throw new Error(`Category data not exists of this id (${categoryId}).`);
                }

                const result = await FinalGoodsAndCategoriesLinkingModel.create({
                    categoryId: categoryId,
                    finalGoodsId: createdFinalGood.dataValues.id
                }, { transaction: transaction, userId: createdBy })

                if (!result) {
                    throw new Error('Error creating final goods category linking.');
                }
            }

            for (const variation of payload.variations) {
                const taxRateResponse = await this._taxRateRepo.getById(variation.taxRateId, transaction)

                if (!taxRateResponse.success) {
                    throw new Error(`Taxrate is not exists of this is id(${variation.taxRateId})`)
                }

                const createdVariation = await FinalGoodsVariationTable.create({
                    name: variation.name,
                    finalGoodsId: createdFinalGood.dataValues.id,
                    discount: variation.discount,
                    taxRateId: variation.taxRateId,
                    sku: variation.sku,
                    price: variation.price,
                    expiryDays: variation.expiryDays,
                    images: variation.images,
                    moq: variation.moq,
                    msq: variation.msq,
                    createdById: createdBy,
                }, { transaction, userId: createdBy });

                if (!createdVariation) {
                    throw new Error("Failed to create final goods variation.");
                }
                const createdStock = await StockTable.create({
                    final_goods_variation_id: createdVariation.dataValues.id,
                    stock: 0,
                    finalGoodsId: createdFinalGood.dataValues.id,
                    createdById: createdBy,
                }, { transaction, userId: createdBy });
                if (!createdStock) {
                    throw new Error("Failed to create stock for final goods variation.");
                }

                for (const attributeValueId of variation.attributeValuesIds) {
                    const attributeResponse = await this._attributeValueRepo.getById(attributeValueId, transaction)
                    if (!attributeResponse.success) {
                        throw new Error(`Attribute value data not exists of this id (${attributeValueId}).`)
                    }
                    await FinalGoodsVariationAndAttributesRelationTable.create({
                        final_goods_variation_id: createdVariation.dataValues.id,
                        attribute_value_id: attributeValueId,
                        createdById: createdBy,
                    }, { transaction, userId: (createdBy) });
                }

            }

            return HelperMethods.getSuccessResponse(null);
        } catch (error) {
            HelperMethods.handleError(error)

            if (error instanceof Error) {
                return HelperMethods.getErrorResponse(error.message)
            } else {
                return HelperMethods.getErrorResponse()
            }
        }
    }

    async update(
        id: number,
        payload: IFinalGoodsUpdatePayload,
        updatedBy: number,
        transaction: Transaction,
    ): Promise<APIBaseResponse<FinalGoodsTable | null>> {
        try {
            const isFinalGoodsExists = await FinalGoodsTable.findByPk(id, { transaction });
            if (!isFinalGoodsExists) {
                throw new Error(`Final goods not found with this id(${id}).`);
            }
            const unitId = payload.unitId
            const itemUnitResponse = await this._itemUnitRepo.getById(unitId, transaction)

            if (!itemUnitResponse.success) {
                throw new Error(`Unit data not exists of this id (${unitId})`)
            }

            await isFinalGoodsExists.update({
                name: payload.name,
                images: payload.images,
                unitId: payload.unitId,
                description: payload.description,
                hsn: payload.hsn,
                status: payload.status,
                updatedById: updatedBy,
            }, {
                userId: updatedBy,
                transaction,
            });
            const isExists = await FinalGoodsAndCategoriesLinkingModel.findOne({
                where: {
                    finalGoodsId: id,
                },
                transaction,
            })
            if (isExists) {
                await FinalGoodsAndCategoriesLinkingModel.destroy({
                    where: {
                        finalGoodsId: id,
                    },
                    transaction,
                    userId: updatedBy
                })
            }
            if (payload.categoryIds.length > 0 && Array.isArray(payload.categoryIds)) {
                for (const categoryId of payload.categoryIds) {
                    const categoryResponse = await this._categoryRepo.getById(categoryId, transaction)
                    if (!categoryResponse.success) {
                        throw new Error(`Category data not exists of this id (${categoryId}).`)
                    }
                    await FinalGoodsAndCategoriesLinkingModel.create({
                        finalGoodsId: id,
                        categoryId: categoryId
                    }, { transaction: transaction, userId: updatedBy })

                }

            }
            return HelperMethods.getSuccessResponse(isFinalGoodsExists);
        } catch (error) {
            HelperMethods.handleError(error)
            if (error instanceof Error) {
                return HelperMethods.getErrorResponse(error.message)
            } else {
                return HelperMethods.getErrorResponse()
            }
        }
    }

    async updateVariation(
        id: number,
        payload: UpdateFinalGoodVariation,
        updatedBy: number,
        transaction: Transaction,
    ): Promise<APIBaseResponse<IFinalGoodVariationNested | null>> {
        try {
            const existingVariation = await FinalGoodsVariationTable.findOne({
                where: {
                    id,
                },
                transaction,
            });
            if (!existingVariation) {
                throw new Error(`Final goods variation not found with this id(${id}).`);
            }

            await existingVariation.update({
                price: payload.price,
                sku: payload.sku,
                taxRateId: payload.taxRateId,
                name: payload.name,
                discount: payload.discount,
                moq: payload.moq,
                msq: payload.msq,
                images: payload.images,
                expiryDays: payload.expiryDays,
                finalGoodsId: payload.finalGoodsId,
                updatedById: updatedBy,
            }, { transaction, userId: updatedBy });


            /* delete old data */
            await FinalGoodsVariationAndAttributesRelationTable.destroy({
                where: {
                    final_goods_variation_id: id,
                },
                force: true,
                transaction,
                userId: updatedBy,
            });

            for (const attributeValueId of payload.attributeValuesIds) {

                /* create new */
                await FinalGoodsVariationAndAttributesRelationTable.create({
                    final_goods_variation_id: id,
                    attribute_value_id: attributeValueId,
                    createdById: updatedBy,
                }, { transaction, userId: (updatedBy) });

            }

            const variation = await this.getVariationById(id, transaction);
            if (!variation.success) {
                throw new Error(`Final goods variation not found with this id(${id}).`);
            }
            return HelperMethods.getSuccessResponse(variation.data);
        } catch (error) {
            HelperMethods.handleError(error)
            if (error instanceof Error) {
                return HelperMethods.getErrorResponse(error.message)
            } else {
                return HelperMethods.getErrorResponse()
            }
        }
    }

    async addNewVariationOfFinalGoods(finalGoodsId: number, payload: FinalGoodsVariationPayload[], createdBy: number, transaction: Transaction): Promise<APIBaseResponse<IFinalGoodVariationNested[] | null>> {
        try {
            const isFinalGoodsExists = await FinalGoodsTable.findByPk(finalGoodsId, { transaction });
            if (!isFinalGoodsExists) {
                throw new Error(`Final goods not found with this id(${finalGoodsId}).`);
            }

            if (!Array.isArray(payload) || payload.length === 0) {
                throw new Error("No variations provided to add.");
            }

            const variations: IFinalGoodVariationNested[] = []

            for (const payloadVariation of payload) {

                const taxRateResponse = await this._taxRateRepo.getById(payloadVariation.taxRateId, transaction)

                if (!taxRateResponse.success) {
                    throw new Error(`Taxrate is not exists of this is id(${payloadVariation.taxRateId})`)
                }

                const createdVariation = await FinalGoodsVariationTable.create({
                    name: payloadVariation.name,
                    finalGoodsId: finalGoodsId,
                    discount: payloadVariation.discount,
                    taxRateId: payloadVariation.taxRateId,
                    sku: payloadVariation.sku,
                    price: payloadVariation.price,
                    expiryDays: payloadVariation.expiryDays,
                    images: payloadVariation.images,
                    moq: payloadVariation.moq,
                    msq: payloadVariation.msq,
                    createdById: createdBy,
                }, { transaction, userId: (createdBy) });

                if (!createdVariation) {
                    throw new Error("Failed to create final goods variation.");
                }

                // Create stock record for the new variation
                const variationData = createdVariation.toJSON();
                const createdStock = await StockTable.create({
                    final_goods_variation_id: variationData.id,
                    stock: 0,
                    finalGoodsId: finalGoodsId,
                    createdById: createdBy,
                }, { transaction, userId: createdBy });

                if (!createdStock) {
                    throw new Error("Failed to create stock for final goods variation.");
                }

                for (const attributeValueId of payloadVariation.attributeValuesIds) {
                    const attributeResponse = await this._attributeValueRepo.getById(attributeValueId, transaction)
                    if (!attributeResponse.success) {
                        throw new Error(`Attribute value data not exists of this id (${attributeValueId}).`)
                    }
                    await FinalGoodsVariationAndAttributesRelationTable.create({
                        final_goods_variation_id: createdVariation.toJSON().id,
                        attribute_value_id: attributeValueId,
                        createdById: createdBy,
                    }, { transaction, userId: (createdBy) });
                }

                const variation = await this.getVariationById(createdVariation.dataValues.id, transaction)
                if (variation.success && variation.data) {
                    variations.push(variation.data);
                }
            }

            return HelperMethods.getSuccessResponse(variations)
        } catch (error) {
            HelperMethods.handleError(error)
            if (error instanceof Error) {
                return HelperMethods.getErrorResponse(error.message)
            } else {
                return HelperMethods.getErrorResponse()
            }
        }
    }

    async getAll(page: number, limit: number, filters: FinalGoodsFilter, transaction: Transaction
    ): Promise<APIBaseResponse<PaginatedBaseResponse<IFinalGoodOverview> | null>> {
        try {
            const where: any = {}
            if (filters.text) {
                where[Op.or] = [
                    { name: { [Op.iLike]: `%${filters.text}%` } },
                    { description: { [Op.iLike]: `%${filters.text}%` } },
                ];
            }


            const paginatedData = await new PaginationProvider<any, FinalGoodsTable>().getPaginatedRecords(FinalGoodsTable, { include: finalGoodsInclude(), limit: limit, page: page, where: where, dateColumn: "createdAt" }, transaction)
            if (paginatedData.rows.length === 0) {
                return HelperMethods.getErrorResponse('data not exists')
            }


            const data: IFinalGoodOverview[] = paginatedData.rows.map(row => {
                return {
                    id: row.dataValues.id,
                    name: row.dataValues.name,
                    description: row.dataValues.description ?? "",
                    hsn: row.dataValues.hsn,
                    images: row.dataValues.images ?? [],
                    status: row.dataValues.status,
                    createdAt: row.dataValues.createdAt,
                    createdBy: {
                        id: row.dataValues.createdById,
                        title: row.createdByUser.dataValues.firstName + ' ' + row.createdByUser.dataValues.lastName,
                    },
                    categories: row.categories.map((category) => {
                        return {
                            id: category.category.dataValues.id,
                            title: category.category.dataValues.name,
                        }
                    }),
                    unit: {
                        id: row.unit.dataValues.id,
                        title: row.unit.dataValues.name,
                    },
                }
            });

            return HelperMethods.getSuccessResponse({
                totalData: paginatedData.total,
                data: data,
                totalPages: paginatedData.totalPages,
                currentPage: paginatedData.currentPage, // Calculate current page
            })
        } catch (error) {
            HelperMethods.handleError(error)
            if (error instanceof Error) {
                return HelperMethods.getErrorResponse(error.message)
            } else {
                return HelperMethods.getErrorResponse()
            }
        }
    }


    async getAllVariation(page: number, limit: number, filters: FinalGoodsVariationFilter, transaction: Transaction
    ): Promise<APIBaseResponse<PaginatedBaseResponse<IFinalGoodVariationNested> | null>> {
        try {
            const where: any = {}
            if (filters.name || filters.sku) {
                where[Op.or] = [
                    { name: { [Op.iLike]: `%${filters.name}%` } },
                    { sku: { [Op.iLike]: `%${filters.sku}%` } },
                ];
            }
            const paginatedData = await new PaginationProvider<any, FinalGoodsVariationTable>().getPaginatedRecords(FinalGoodsVariationTable, { include: finalGoodsVariationsInclude(), limit: limit, page: page, where: where, dateColumn: "createdAt" }, transaction);

            const data: IFinalGoodVariationNested[] = paginatedData.rows.map(row => {
                return {
                    id: row.dataValues.id,
                    tax: {
                        id: row.tax.dataValues.id,
                        title: row.tax.dataValues.title,
                    },
                    name: row.dataValues.name,
                    sku: row.dataValues.sku,
                    price: Number(row.dataValues.price),
                    expiryDays: Number(row.dataValues.expiryDays),
                    discount: Number(row.dataValues.discount),
                    moq: Number(row.dataValues.moq ?? 0),
                    msq: Number(row.dataValues.msq),
                    images: row.dataValues.images ?? [],
                    attributes: row.variationAttributes.map((attribute) => {
                        return {
                            attribute: {
                                ...attribute.attribute.dataValues,
                            },
                            attributeValue: {
                                ...attribute.attributeValue.dataValues,
                            },
                        }
                    }),
                }
            });



            return HelperMethods.getSuccessResponse({
                totalData: paginatedData.total,
                data: data,
                totalPages: paginatedData.totalPages,
                currentPage: paginatedData.currentPage,
            })
        } catch (error) {
            HelperMethods.handleError(error)
            if (error instanceof Error) {
                return HelperMethods.getErrorResponse(error.message)
            } else {
                return HelperMethods.getErrorResponse()
            }
        }
    }

    async getById(
        id: number,
        transaction: Transaction,
    ): Promise<APIBaseResponse<IFinalGoodDetailed | null>> {
        try {
            const fetchedFinalGood = await FinalGoodsTable.findByPk(id, {
                transaction,
                include: [
                    ...finalGoodsInclude(),
                    {
                        model: FinalGoodsVariationTable,
                        as: 'variations',
                        include: finalGoodsVariationsInclude()
                    }
                ]
            })

            if (!fetchedFinalGood) throw new Error(`Final goods not of this id(${id})`);



            const variations = fetchedFinalGood.variations;

            const fetchVariations: IFinalGoodVariationNested[] = [];
            variations.forEach(async (variation: any) => {
                /* fetch variations by getVariationById, if all is ok then add to the array else throw error */
                const variationResponse = await this.getVariationById(variation.id, transaction);
                if (variationResponse.success) {
                    fetchVariations.push(variationResponse.data!);
                } else {
                    throw new Error(variationResponse.message);
                }
            });

            const finalGoodsData: IFinalGoodDetailed = {
                id: fetchedFinalGood.dataValues.id,
                name: fetchedFinalGood.dataValues.name,
                description: fetchedFinalGood.dataValues.description ?? "",
                hsn: fetchedFinalGood.dataValues.hsn,
                images: fetchedFinalGood.dataValues.images ?? [],
                status: fetchedFinalGood.dataValues.status,
                createdAt: fetchedFinalGood.dataValues.createdAt,
                createdBy: {
                    id: fetchedFinalGood.dataValues.createdById,
                    title: fetchedFinalGood.createdByUser.dataValues.firstName + ' ' + fetchedFinalGood.createdByUser.dataValues.lastName,
                },
                categories: fetchedFinalGood.categories.map((category) => {
                    return {
                        id: category.category.dataValues.id,
                        title: category.category.dataValues.name,
                    }
                }),
                unit: {
                    id: fetchedFinalGood.unit.dataValues.id,
                    title: fetchedFinalGood.unit.dataValues.name,
                },
                variations: fetchVariations,
            };


            return HelperMethods.getSuccessResponse(finalGoodsData);
        } catch (error) {
            HelperMethods.handleError(error)
            if (error instanceof Error) {
                return HelperMethods.getErrorResponse(error.message)
            } else {
                return HelperMethods.getErrorResponse()
            }
        }
    }
    async getVariationById(
        id: number,
        transaction: Transaction,
    ): Promise<APIBaseResponse<IFinalGoodVariationNested | null>> {
        try {
            const variation = await FinalGoodsVariationTable.findByPk(id, {
                include: finalGoodsVariationsInclude()
            })

            if (!variation) throw new Error(`Variation not found of this id(${id})`)
            return HelperMethods.getSuccessResponse({
                id: variation.dataValues.id,
                tax: {
                    id: variation.tax.dataValues.id,
                    title: variation.tax.dataValues.title,
                },
                name: variation.dataValues.name,
                sku: variation.dataValues.sku,
                price: Number(variation.dataValues.price),
                expiryDays: Number(variation.dataValues.expiryDays),
                discount: Number(variation.dataValues.discount),
                moq: Number(variation.dataValues.moq ?? 0),
                msq: Number(variation.dataValues.msq),
                images: variation.dataValues.images ?? [],
                attributes: variation.variationAttributes.map((attribute) => {
                    return {
                        attribute: {
                            ...attribute.attribute.dataValues,
                        },
                        attributeValue: {
                            ...attribute.attributeValue.dataValues,
                        },
                    }
                }),
            });
        } catch (error) {
            HelperMethods.handleError(error)
            if (error instanceof Error) {
                return HelperMethods.getErrorResponse(error.message)
            } else {
                return HelperMethods.getErrorResponse()
            }
        }
    }

    async delete(
        id: number,
        user_id: number,
        transaction: Transaction,
    ): Promise<APIBaseResponse<FinalGoodsTable | null>> {

        try {
            const isExists = await FinalGoodsTable.findByPk(id)
            if (!isExists) throw new Error('final goods not exists')

            const response = await FinalGoodsTable.destroy({
                where: {
                    id: id,
                },
                userId: (user_id),
                transaction
            });

            if (response === 0) {
                return HelperMethods.getErrorResponse("deletion failed")
            }
            return HelperMethods.getSuccessResponse(isExists, 'Data deleted successfully')
        } catch (error) {
            HelperMethods.handleError(error);
            if (error instanceof Error) {
                return HelperMethods.getErrorResponse(error.message)
            } else {
                return HelperMethods.getErrorResponse()
            }
        }
    }


    async deleteVariation(
        ids: number[],
        user_id: number,
        transaction: Transaction,
    ): Promise<APIBaseResponse<null>> {

        try {
            console.log(ids)
            for (const id of ids) {
                const isExists = await FinalGoodsVariationTable.findByPk(id)
                if (!isExists) {
                    throw new Error(`Data not exists of this id ${id}`)
                }
            }

            const response = await FinalGoodsVariationTable.destroy({
                where: {
                    id: ids,
                },
                userId: user_id,
                transaction
            });

            if (response === 0) {
                return HelperMethods.getErrorResponse("deletion failed")
            }
            return HelperMethods.getSuccessResponse(null, 'Data deleted successfully')
        } catch (error) {
            HelperMethods.handleError(error);
            if (error instanceof Error) {
                return HelperMethods.getErrorResponse(error.message)
            } else {
                return HelperMethods.getErrorResponse()
            }
        }
    }

}
