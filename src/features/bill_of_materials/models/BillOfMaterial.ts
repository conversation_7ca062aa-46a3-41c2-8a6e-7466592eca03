import { Optional } from "sequelize";
import { BaseMeta, MetaUser, ParsedMeta } from "../../../core/CoreInterfaces";
import { IRawMaterialVariationResponse } from "../../raw_material/models/IRawMaterialAndVariations";
import { IFinalGoodVariationNested } from "../../final_goods/interface/IFinalGood";

interface BillOfMaterialAttributes extends BaseMeta {
    finalGoodsVariationId: number;
}

interface ICreateBillOfMaterialAttributes extends Optional<BillOfMaterialAttributes, "id" | "createdAt" | "updatedAt" | "deletedAt"> { }


interface BillOfMaterialRawMaterialTableAttributes extends BaseMeta {
    bomId: number;
    rawMaterialVariationId: number;
    qty: number;
}

interface ICreateBillOfMaterialRawMaterialTableAttributes extends Optional<BillOfMaterialRawMaterialTableAttributes, "id" | "createdAt" | "updatedAt" | "deletedAt"> { }


interface BillOfMaterialRawMaterialPayload {
    bomId: number;
    rawMaterialVariationId: number;
    qty: number;
}

interface BillOfMaterialPayload {
    finalGoodsVariationId: number;
    rawMaterials: BillOfMaterialRawMaterialPayload[];
}

interface BillOfMaterialUpdatePayload extends Omit<BillOfMaterialPayload,'finalGoodsId'>{}

interface ParsedBillOfMaterialRawMaterial extends ParsedMeta {
    id: number;
    bomId: number;
    rawMaterialVariationId: number;
    qty: number;
}
interface rawMaterialVariation {
    id: number,
    qty: number,
    name: string
}
interface ParsedBillOfMaterial extends ParsedMeta {
    id: number;
    finalGoodsVariation: IFinalGoodVariationNested;
    rawMaterialVariation: rawMaterialVariation[]
}
interface BomList  {
    id: number;
    finalGoodsVariation: IFinalGoodVariationNested;
    createdBy: MetaUser;
    // updatedBy: MetaUser | null;
    // deletedBy: MetaUser | null;
    createdAt: Date;
    // updatedAt: Date | null;
    // deletedAt: Date | null;
} 


interface IBomGetSingleResponse {
     id: number;
    finalGoodsVariation: IFinalGoodVariationNested;
    rawMaterialVariations: IBillOfMaterialRawMaterialVariation[];
    createdAt: Date;
}


interface IBillOfMaterialRawMaterialVariation {
    id: number;
    bomId: number;
    rawMaterialVariation: IRawMaterialVariationResponse;
    qty: number;
}


export {
    BillOfMaterialAttributes,
    ICreateBillOfMaterialAttributes,
    BillOfMaterialRawMaterialTableAttributes,
    ICreateBillOfMaterialRawMaterialTableAttributes,
    BillOfMaterialPayload,
    ParsedBillOfMaterial,
    ParsedBillOfMaterialRawMaterial,
    IBomGetSingleResponse,
    BillOfMaterialUpdatePayload,
    BomList
};