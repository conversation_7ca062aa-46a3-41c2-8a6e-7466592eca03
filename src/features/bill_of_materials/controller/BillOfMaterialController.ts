import { Request, Response, NextFunction } from "express";
import { HelperMethods } from "../../../core/HelperMethods";
import { sequelizeInit } from "../../../sequelize_init";
import { get } from "lodash";
import { RepoProvider } from "../../../core/RepoProvider";
import { ERROR_MESSAGE } from "../../../core/constants";

class BillOfMaterialController {
    static async create(req: Request, res: Response, next: NextFunction) {
        const transaction = await sequelizeInit.transaction();
        try {
            const payload: any = req?.body;
            const user_id = 1// get(req, "user_id");
            if (!user_id || isNaN(user_id)) {
                throw new Error(ERROR_MESSAGE.USER_ID_SHOULD_BE_NUMBER)
            }
            const bomDetails = await RepoProvider.bomRepo.create(
                payload,
                user_id,
                transaction
            );
            if (!bomDetails.success) {
                throw new Error(bomDetails.message || "Failed to create Bill of Materials");
            }
            await transaction.commit();
            res.status(200).send(bomDetails);
        } catch (error) {
            await transaction.rollback();
            res.status(500).send(
                HelperMethods.getErrorResponse(
                    (error instanceof Error ? error.message : "An error occurred while creating bill of materials."),
                )
            );
        }
    }

    static async update(req: Request, res: Response, next: NextFunction) {
        const transaction = await sequelizeInit.transaction();
        try {
            const payload: any = req?.body;
            const user_id = get(req, "user_id");
            if (!user_id || isNaN(user_id)) {
                throw new Error(ERROR_MESSAGE.USER_ID_SHOULD_BE_NUMBER)
            }
            const id = parseInt(get(req.params, "id"));
            if (!id || isNaN(id)) {
                throw new Error(ERROR_MESSAGE.ID_SHOULD_BE_NUMBER)
            }
            const bomDetails = await RepoProvider.bomRepo.update(
                id,
                payload,
                user_id,
                transaction
            );
            if (!bomDetails.success) {
                throw new Error(bomDetails.message || "Failed to update Bill of Materials");
            }
            await transaction.commit();
            res.status(200).send(bomDetails);
        } catch (error) {
            await transaction.rollback();
            res.status(500).send(
                HelperMethods.getErrorResponse(
                    (error instanceof Error ? error.message : "An error occurred while updating bill of materials."),
                )
            );
        }
    }

   
    static async getById(req: Request, res: Response, next: NextFunction) {
        const transaction = await sequelizeInit.transaction();
        try {
            const id = parseInt(get(req.params, "id"));
            if (!id || isNaN(id)) {
                throw new Error(ERROR_MESSAGE.ID_SHOULD_BE_NUMBER);
            }
            const bomDetails = await RepoProvider.bomRepo.getById(id, transaction);
            if (!bomDetails.success) {
                throw new Error(bomDetails.message || "Failed to fetch Bill of Materials");
            }
            await transaction.commit();
            res.status(200).send(bomDetails);
        } catch (error) {
            await transaction.rollback();
            res.status(500).send(
                HelperMethods.getErrorResponse(
                    (error instanceof Error ? error.message : "An error occurred while fetching bill of materials."),
                )
            );
        }
    }

     static async delete(req: Request, res: Response, next: NextFunction) {
        const transaction = await sequelizeInit.transaction();
        try {
            const user_id = get(req, "user_id");
            if (!user_id || isNaN(user_id)) {
                throw new Error(ERROR_MESSAGE.USER_ID_SHOULD_BE_NUMBER)
            }
            const id = parseInt(get(req.params, "id"));
            if (!id || isNaN(id)) {
                throw new Error(ERROR_MESSAGE.ID_SHOULD_BE_NUMBER)
            }
            const finalGoodsDetails = await RepoProvider.bomRepo.delete(
                id,
                user_id,
                transaction
            );
            if (!finalGoodsDetails.success) {
                throw new Error(finalGoodsDetails.message || "Failed to delete Bill of Materials");
            }
            await transaction.commit();
            res.status(200).send(finalGoodsDetails);
        } catch (error) {
            await transaction.rollback();
            res.status(500).send(
                HelperMethods.getErrorResponse(
                    (error instanceof Error ? error.message : "An error occurred while deleting bill of materials."),
                )
            );
        }
    }

    static async getAll(req: Request, res: Response, next: NextFunction) {
    const transaction = await sequelizeInit.transaction();
    try {
        const page = !isNaN(Number(get(req.query, "page"))) ? Number(get(req.query, "page")) : 1;
        const pageSize = !isNaN(Number(get(req.query, "pageSize"))) ? Number(get(req.query, "pageSize")) : 10;
        const search = (get(req.query, "search") || "").toString().trim();

        const finalGoodsDetails = await RepoProvider.bomRepo.getAll(page, pageSize, transaction, search);
        
        // await transaction.commit();

        if (!finalGoodsDetails.success) {
            throw new Error(finalGoodsDetails.message || "Failed to fetch Bill of Materials");
        }

        res.status(200).send(finalGoodsDetails);
    } catch (error) {
        // await transaction.rollback();
        res.status(500).send(
            HelperMethods.getErrorResponse(
                (error instanceof Error ? error.message : "An error occurred while fetching bill of materials."),
            )
        );
    }
}

    
}

export { BillOfMaterialController };